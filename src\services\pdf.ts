import * as fs from "fs";
import * as path from "path";
import pdfParse from "pdf-parse";
import { v4 as uuidv4 } from "uuid";
import NodeCache from "node-cache";

export interface PDFDocument {
  id: string;
  name: string;
  path: string;
  content: string;
  chunks: PDFChunk[];
  metadata: {
    pageCount: number;
    createdAt: Date;
    fileSize: number;
  };
}

export interface PDFChunk {
  id: string;
  documentId: string;
  content: string;
  metadata: {
    pageNumber?: number;
    section?: string;
  };
}

export class PDFService {
  private pdfCache: NodeCache;
  private pdfStoragePath: string;
  private documents: Map<string, PDFDocument>;

  constructor() {
    this.pdfCache = new NodeCache({ stdTTL: 3600, checkperiod: 600 });
    this.pdfStoragePath = path.join(process.cwd(), "uploads", "pdfs");
    this.documents = new Map<string, PDFDocument>();

    // Ensure the upload directory exists
    if (!fs.existsSync(this.pdfStoragePath)) {
      fs.mkdirSync(this.pdfStoragePath, { recursive: true });
    }

    // Load any existing PDFs from the storage directory
    this.loadExistingPDFs();
  }

  private async loadExistingPDFs(): Promise<void> {
    try {
      if (!fs.existsSync(this.pdfStoragePath)) return;

      const files = fs.readdirSync(this.pdfStoragePath);
      for (const file of files) {
        if (file.endsWith(".pdf")) {
          const filePath = path.join(this.pdfStoragePath, file);
          const stats = fs.statSync(filePath);

          if (stats.isFile()) {
            try {
              // Process the PDF but don't store in cache yet
              const documentId = path.basename(file, ".pdf");
              await this.processPDF(filePath, documentId, file);
            } catch (error) {
              console.error(`Error loading PDF ${file}:`, error);
            }
          }
        }
      }
      console.log(`Loaded ${this.documents.size} PDFs from storage`);
    } catch (error) {
      console.error("Error loading existing PDFs:", error);
    }
  }

  /**
   * Process and store a new PDF
   */
  public async processPDF(
    filePath: string,
    documentId: string = uuidv4(),
    originalName: string = ""
  ): Promise<string> {
    try {
      // Read the PDF file
      const dataBuffer = fs.readFileSync(filePath);
      const pdfData = await pdfParse(dataBuffer);

      // Extract text content
      const pdfText = pdfData.text;

      // Create chunks from the PDF content
      const chunks = this.createChunks(pdfText, documentId);

      // Store document metadata
      const document: PDFDocument = {
        id: documentId,
        name: originalName || path.basename(filePath),
        path: filePath,
        content: pdfText,
        chunks: chunks,
        metadata: {
          pageCount: pdfData.numpages,
          createdAt: new Date(),
          fileSize: dataBuffer.length,
        },
      };

      // Store in our documents map
      this.documents.set(documentId, document);

      // Store in cache for quick access
      this.pdfCache.set(`pdf_${documentId}`, pdfText);

      console.log(
        `Processed PDF ${document.name} with ${chunks.length} chunks`
      );

      return documentId;
    } catch (error) {
      console.error("Error processing PDF:", error);
      throw new Error("Failed to process PDF");
    }
  }

  /**
   * Upload a new PDF file
   */
  public async uploadPDF(
    tempFilePath: string,
    originalName: string
  ): Promise<string> {
    try {
      const documentId = uuidv4();
      const fileName = `${documentId}.pdf`;
      const destinationPath = path.join(this.pdfStoragePath, fileName);

      // Copy the file to our storage location
      fs.copyFileSync(tempFilePath, destinationPath);

      // Process the PDF
      await this.processPDF(destinationPath, documentId, originalName);

      // Remove the temporary file
      fs.unlinkSync(tempFilePath);

      return documentId;
    } catch (error) {
      console.error("Error uploading PDF:", error);
      throw new Error("Failed to upload PDF");
    }
  }

  /**
   * Create chunks from PDF text
   */
  private createChunks(text: string, documentId: string): PDFChunk[] {
    // Split by paragraphs (multiple newlines)
    const paragraphs = text.split(/\n{2,}/);

    // Filter out empty paragraphs and create chunks
    const chunks = paragraphs
      .filter((paragraph) => paragraph.trim().length > 30) // Only keep substantial paragraphs
      .map((paragraph) => ({
        id: uuidv4(),
        documentId,
        content: paragraph.trim(),
        metadata: {},
      }));

    // Also create additional chunks by splitting on section headers
    // This helps capture important sections like "Tugas Pokok dan Fungsi"
    const sectionHeaders = [
      /tugas\s+pokok\s+dan\s+fungsi/i,
      /visi\s+dan\s+misi/i,
      /struktur\s+organisasi/i,
      /layanan/i,
      /profil/i,
    ];

    const additionalChunks: PDFChunk[] = [];
    for (const header of sectionHeaders) {
      const match = text.match(header);
      if (match) {
        const startIndex = match.index!;
        // Extract a reasonable chunk around the section (500 characters)
        const sectionText = text.substring(startIndex, startIndex + 500);
        if (sectionText.trim().length > 50) {
          additionalChunks.push({
            id: uuidv4(),
            documentId,
            content: sectionText.trim(),
            metadata: { section: match[0] },
          });
        }
      }
    }

    return [...chunks, ...additionalChunks];
  }

  /**
   * Get all chunks for a document
   */
  public getDocumentChunks(documentId: string): PDFChunk[] {
    const document = this.documents.get(documentId);
    if (!document) {
      throw new Error(`Document with ID ${documentId} not found`);
    }
    return document.chunks;
  }

  /**
   * Get all documents
   */
  public getAllDocuments(): PDFDocument[] {
    return Array.from(this.documents.values());
  }

  /**
   * Get a document by ID
   */
  public getDocument(documentId: string): PDFDocument | undefined {
    return this.documents.get(documentId);
  }

  /**
   * Delete a document
   */
  public deleteDocument(documentId: string): boolean {
    const document = this.documents.get(documentId);
    if (!document) {
      return false;
    }

    // Remove from cache
    this.pdfCache.del(`pdf_${documentId}`);

    // Remove from documents map
    this.documents.delete(documentId);

    // Delete the file if it exists
    try {
      if (fs.existsSync(document.path)) {
        fs.unlinkSync(document.path);
      }
    } catch (error) {
      console.error(`Error deleting PDF file ${document.path}:`, error);
    }

    return true;
  }

  /**
   * Simple text search in PDF documents
   */
  public searchInDocuments(
    query: string,
    documentIds?: string[]
  ): { chunks: PDFChunk[]; documentIds: string[] } {
    const docsToSearch = documentIds
      ? (documentIds
          .map((id) => this.documents.get(id))
          .filter(Boolean) as PDFDocument[])
      : Array.from(this.documents.values());

    if (docsToSearch.length === 0) {
      return { chunks: [], documentIds: [] };
    }

    const results: PDFChunk[] = [];
    const matchedDocIds = new Set<string>();

    // Simple keyword search in chunks
    // Lowered minimum term length for Indonesian language (includes words like "dan", "apa")
    const queryTerms = query
      .toLowerCase()
      .split(/\s+/)
      .filter((term) => term.length > 2);

    for (const doc of docsToSearch) {
      let docHasMatch = false;

      for (const chunk of doc.chunks) {
        const chunkText = chunk.content.toLowerCase();

        // Check if chunk contains any of the query terms
        const matchesTerms = queryTerms.some((term) =>
          chunkText.includes(term)
        );

        if (matchesTerms) {
          results.push(chunk);
          docHasMatch = true;
        }
      }

      if (docHasMatch) {
        matchedDocIds.add(doc.id);
      }
    }

    return {
      chunks: results,
      documentIds: Array.from(matchedDocIds),
    };
  }
}
