{"name": "chatbot-backend", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "nodemon src/app.ts", "watch": "tsc -w"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.22.0", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "pdf-parse": "^1.1.1", "pg": "^8.13.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/express": "^5.0.0", "@types/multer": "^1.4.11", "@types/node": "^22.13.4", "@types/pdf-parse": "^1.1.4", "@types/pg": "^8.11.11", "@types/socket.io": "^3.0.1", "@types/uuid": "^10.0.0", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}