require('dotenv').config();
const { GoogleGenerativeAI } = require('@google/generative-ai');

// Test the search functionality with your specific query
async function testSearchDebug() {
  try {
    console.log('Testing search for: "apa tugas pokok dan fungsi dpmptsp"');
    
    // Test text search logic
    const query = "apa tugas pokok dan fungsi dpmptsp";
    const pdfContent = `Tugas Pokok dan Fungsi
DPMPTSP Kota Pekanbaru bertugas merumuskan dan melaksanakan kebijakan bidang
penanaman modal serta menyelenggarakan pelayanan administrasi penanaman modal, perizinan,
dan non-perizinan secara terpadu. Fungsi utamanya meliputi perencanaan, perumusan kebijakan
teknis, penyelenggaraan pelayanan terpadu, pembinaan, pengendalian, koordinasi, fasilitasi,
peningkatan iklim investasi, penyusunan profil investasi, evaluasi, dan pelaporan terkait
penanaman modal dan perizinan/non- perizinan di Kota Pekanbaru, berdasar<PERSON> prinsip
koordinasi, integrasi, sinkron<PERSON>si, simplifikasi, keamanan, kepastian, dan transparansi.`;

    console.log('\n=== Testing Text Search Logic ===');
    
    // Simulate the text search logic from pdf.ts
    const queryTerms = query
      .toLowerCase()
      .split(/\s+/)
      .filter((term) => term.length > 3);
    
    console.log('Query terms (>3 chars):', queryTerms);
    
    const chunkText = pdfContent.toLowerCase();
    const matchesTerms = queryTerms.some((term) => chunkText.includes(term));
    
    console.log('PDF content contains matching terms:', matchesTerms);
    
    // Test each term individually
    queryTerms.forEach(term => {
      const found = chunkText.includes(term);
      console.log(`  "${term}": ${found}`);
    });
    
    console.log('\n=== Testing Vector Similarity ===');
    
    // Test vector similarity
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const embeddingModel = genAI.getGenerativeModel({ model: 'embedding-001' });
    
    // Generate embeddings
    const queryResult = await embeddingModel.embedContent(query);
    const contentResult = await embeddingModel.embedContent(pdfContent);
    
    const queryVector = queryResult.embedding.values;
    const contentVector = contentResult.embedding.values;
    
    // Calculate cosine similarity
    function cosineSimilarity(vecA, vecB) {
      if (vecA.length !== vecB.length) {
        throw new Error("Vectors must have the same dimensions");
      }

      let dotProduct = 0;
      let normA = 0;
      let normB = 0;

      for (let i = 0; i < vecA.length; i++) {
        dotProduct += vecA[i] * vecB[i];
        normA += vecA[i] * vecA[i];
        normB += vecB[i] * vecB[i];
      }

      if (normA === 0 || normB === 0) {
        return 0;
      }

      return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    
    const similarity = cosineSimilarity(queryVector, contentVector);
    console.log(`Similarity score: ${similarity.toFixed(4)}`);
    console.log(`Current threshold: 0.5`);
    console.log(`Would pass threshold: ${similarity >= 0.5}`);
    
    // Test with different variations of the query
    console.log('\n=== Testing Query Variations ===');
    
    const variations = [
      "tugas pokok fungsi dpmptsp",
      "tugas dpmptsp",
      "fungsi dpmptsp", 
      "tugas pokok dan fungsi",
      "dpmptsp tugas"
    ];
    
    for (const variation of variations) {
      const varResult = await embeddingModel.embedContent(variation);
      const varVector = varResult.embedding.values;
      const varSimilarity = cosineSimilarity(varVector, contentVector);
      console.log(`"${variation}": ${varSimilarity.toFixed(4)}`);
    }
    
  } catch (error) {
    console.error('Error in test:', error);
  }
}

testSearchDebug();
