"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PDFService = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const pdf_parse_1 = __importDefault(require("pdf-parse"));
const uuid_1 = require("uuid");
const node_cache_1 = __importDefault(require("node-cache"));
class PDFService {
    constructor() {
        this.pdfCache = new node_cache_1.default({ stdTTL: 3600, checkperiod: 600 });
        this.pdfStoragePath = path.join(process.cwd(), "uploads", "pdfs");
        this.documents = new Map();
        // Ensure the upload directory exists
        if (!fs.existsSync(this.pdfStoragePath)) {
            fs.mkdirSync(this.pdfStoragePath, { recursive: true });
        }
        // Load any existing PDFs from the storage directory
        this.loadExistingPDFs();
    }
    async loadExistingPDFs() {
        try {
            if (!fs.existsSync(this.pdfStoragePath))
                return;
            const files = fs.readdirSync(this.pdfStoragePath);
            for (const file of files) {
                if (file.endsWith(".pdf")) {
                    const filePath = path.join(this.pdfStoragePath, file);
                    const stats = fs.statSync(filePath);
                    if (stats.isFile()) {
                        try {
                            // Process the PDF but don't store in cache yet
                            const documentId = path.basename(file, ".pdf");
                            await this.processPDF(filePath, documentId, file);
                        }
                        catch (error) {
                            console.error(`Error loading PDF ${file}:`, error);
                        }
                    }
                }
            }
            console.log(`Loaded ${this.documents.size} PDFs from storage`);
        }
        catch (error) {
            console.error("Error loading existing PDFs:", error);
        }
    }
    /**
     * Process and store a new PDF
     */
    async processPDF(filePath, documentId = (0, uuid_1.v4)(), originalName = "") {
        try {
            // Read the PDF file
            const dataBuffer = fs.readFileSync(filePath);
            const pdfData = await (0, pdf_parse_1.default)(dataBuffer);
            // Extract text content
            const pdfText = pdfData.text;
            // Create chunks from the PDF content
            const chunks = this.createChunks(pdfText, documentId);
            // Store document metadata
            const document = {
                id: documentId,
                name: originalName || path.basename(filePath),
                path: filePath,
                content: pdfText,
                chunks: chunks,
                metadata: {
                    pageCount: pdfData.numpages,
                    createdAt: new Date(),
                    fileSize: dataBuffer.length,
                },
            };
            // Store in our documents map
            this.documents.set(documentId, document);
            // Store in cache for quick access
            this.pdfCache.set(`pdf_${documentId}`, pdfText);
            console.log(`Processed PDF ${document.name} with ${chunks.length} chunks`);
            return documentId;
        }
        catch (error) {
            console.error("Error processing PDF:", error);
            throw new Error("Failed to process PDF");
        }
    }
    /**
     * Upload a new PDF file
     */
    async uploadPDF(tempFilePath, originalName) {
        try {
            const documentId = (0, uuid_1.v4)();
            const fileName = `${documentId}.pdf`;
            const destinationPath = path.join(this.pdfStoragePath, fileName);
            // Copy the file to our storage location
            fs.copyFileSync(tempFilePath, destinationPath);
            // Process the PDF
            await this.processPDF(destinationPath, documentId, originalName);
            // Remove the temporary file
            fs.unlinkSync(tempFilePath);
            return documentId;
        }
        catch (error) {
            console.error("Error uploading PDF:", error);
            throw new Error("Failed to upload PDF");
        }
    }
    /**
     * Create chunks from PDF text
     */
    createChunks(text, documentId) {
        console.log(`Creating chunks for document ${documentId}, text length: ${text.length}`);
        // Split by paragraphs (multiple newlines)
        const paragraphs = text.split(/\n{2,}/);
        // Filter out empty paragraphs and create chunks
        const chunks = paragraphs
            .filter((paragraph) => paragraph.trim().length > 30) // Only keep substantial paragraphs
            .map((paragraph) => ({
            id: (0, uuid_1.v4)(),
            documentId,
            content: paragraph.trim(),
            metadata: {},
        }));
        console.log(`Created ${chunks.length} paragraph chunks`);
        // Also create additional chunks by splitting on section headers
        // This helps capture important sections like "Tugas Pokok dan Fungsi"
        const sectionHeaders = [
            {
                pattern: /tugas\s+pokok\s+dan\s+fungsi/i,
                name: "Tugas Pokok dan Fungsi",
            },
            { pattern: /visi\s+dan\s+misi/i, name: "Visi dan Misi" },
            { pattern: /struktur\s+organisasi/i, name: "Struktur Organisasi" },
            { pattern: /layanan/i, name: "Layanan" },
            { pattern: /profil/i, name: "Profil" },
            { pattern: /dpmptsp/i, name: "DPMPTSP" },
        ];
        const additionalChunks = [];
        for (const header of sectionHeaders) {
            const matches = [
                ...text.matchAll(new RegExp(header.pattern.source, "gi")),
            ];
            for (const match of matches) {
                if (match.index !== undefined) {
                    const startIndex = match.index;
                    // Extract a larger chunk around the section (800 characters)
                    // Also look backwards to capture any preceding context
                    const contextStart = Math.max(0, startIndex - 100);
                    const sectionText = text.substring(contextStart, startIndex + 700);
                    if (sectionText.trim().length > 50) {
                        console.log(`Found section "${header.name}" at index ${startIndex}`);
                        additionalChunks.push({
                            id: (0, uuid_1.v4)(),
                            documentId,
                            content: sectionText.trim(),
                            metadata: { section: header.name },
                        });
                    }
                }
            }
        }
        console.log(`Created ${additionalChunks.length} section-based chunks`);
        // Create overlapping chunks for better context preservation
        const overlappingChunks = [];
        const chunkSize = 400;
        const overlapSize = 100;
        for (let i = 0; i < text.length; i += chunkSize - overlapSize) {
            const chunkText = text.substring(i, i + chunkSize);
            if (chunkText.trim().length > 100) {
                overlappingChunks.push({
                    id: (0, uuid_1.v4)(),
                    documentId,
                    content: chunkText.trim(),
                    metadata: { type: "overlapping", startIndex: i },
                });
            }
        }
        console.log(`Created ${overlappingChunks.length} overlapping chunks`);
        const allChunks = [...chunks, ...additionalChunks, ...overlappingChunks];
        console.log(`Total chunks created: ${allChunks.length}`);
        return allChunks;
    }
    /**
     * Get all chunks for a document
     */
    getDocumentChunks(documentId) {
        const document = this.documents.get(documentId);
        if (!document) {
            throw new Error(`Document with ID ${documentId} not found`);
        }
        return document.chunks;
    }
    /**
     * Get all documents
     */
    getAllDocuments() {
        return Array.from(this.documents.values());
    }
    /**
     * Get a document by ID
     */
    getDocument(documentId) {
        return this.documents.get(documentId);
    }
    /**
     * Delete a document
     */
    deleteDocument(documentId) {
        const document = this.documents.get(documentId);
        if (!document) {
            return false;
        }
        // Remove from cache
        this.pdfCache.del(`pdf_${documentId}`);
        // Remove from documents map
        this.documents.delete(documentId);
        // Delete the file if it exists
        try {
            if (fs.existsSync(document.path)) {
                fs.unlinkSync(document.path);
            }
        }
        catch (error) {
            console.error(`Error deleting PDF file ${document.path}:`, error);
        }
        return true;
    }
    /**
     * Simple text search in PDF documents
     */
    searchInDocuments(query, documentIds) {
        const docsToSearch = documentIds
            ? documentIds
                .map((id) => this.documents.get(id))
                .filter(Boolean)
            : Array.from(this.documents.values());
        if (docsToSearch.length === 0) {
            return { chunks: [], documentIds: [] };
        }
        const results = [];
        const matchedDocIds = new Set();
        // Improved keyword search for Indonesian language
        // Reduced minimum term length to 2 to include important words like "dan", "apa"
        const queryTerms = query
            .toLowerCase()
            .split(/\s+/)
            .filter((term) => term.length >= 2);
        // Add specific Indonesian keywords that should be prioritized
        const importantKeywords = [
            "tugas",
            "pokok",
            "fungsi",
            "dpmptsp",
            "dpmtpsp",
            "layanan",
            "perizinan",
            "investasi",
            "visi",
            "misi",
        ];
        console.log(`Text search query: "${query}"`);
        console.log(`Search terms: [${queryTerms.join(", ")}]`);
        for (const doc of docsToSearch) {
            let docHasMatch = false;
            const docChunks = [];
            for (const chunk of doc.chunks) {
                const chunkText = chunk.content.toLowerCase();
                let score = 0;
                // Check for exact phrase matches first (higher score)
                if (chunkText.includes("tugas pokok dan fungsi")) {
                    score += 10;
                }
                if (chunkText.includes("dpmptsp") || chunkText.includes("dpmtpsp")) {
                    score += 5;
                }
                // Check for individual term matches
                for (const term of queryTerms) {
                    if (chunkText.includes(term)) {
                        // Give higher score to important keywords
                        if (importantKeywords.includes(term)) {
                            score += 3;
                        }
                        else {
                            score += 1;
                        }
                    }
                }
                // If chunk has any matches, add it with its score
                if (score > 0) {
                    docChunks.push({ chunk, score });
                    docHasMatch = true;
                }
            }
            if (docHasMatch) {
                // Sort chunks by score (highest first) and add to results
                docChunks.sort((a, b) => b.score - a.score);
                results.push(...docChunks.map((item) => item.chunk));
                matchedDocIds.add(doc.id);
            }
        }
        console.log(`Text search found ${results.length} matching chunks`);
        return {
            chunks: results,
            documentIds: Array.from(matchedDocIds),
        };
    }
}
exports.PDFService = PDFService;
